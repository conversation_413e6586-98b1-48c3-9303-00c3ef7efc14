"""
evaluate_local_whisper.py

This script:
 - Loads a local Whisper-tiny model
 - Searches audio files in ./test-data/
 - Uses a predefined list of ground-truth strings (ordered by filenames)
 - Evaluates WER, CER, SER, RTF
 - Saves results to results.csv and metrics.txt
"""

import os
import time
import torch
import librosa
import soundfile as sf
import numpy as np
import csv

from jiwer import wer, cer, Compose, ToLowerCase, RemovePunctuation, RemoveMultipleSpaces, Strip
from transformers import WhisperProcessor, WhisperForConditionalGeneration

# ---------- normalization pipeline ----------
normalization = Compose([
    ToLowerCase(),
    RemovePunctuation(),
    RemoveMultipleSpaces(),
    Strip()
])

# ---------- config ----------
AUDIO_DIR = "test-data"
MODEL_DIR = "./whisper-tiny-ar-quran"   # path where you saved your model
RESULTS_CSV = "audio_predictions.csv"
METRICS_TXT = "metrics.txt"
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

# ---------- load model ----------
print(f"Loading model from {MODEL_DIR} on {DEVICE} ...")
processor = WhisperProcessor.from_pretrained(MODEL_DIR)
model = WhisperForConditionalGeneration.from_pretrained(MODEL_DIR).to(DEVICE)

# ---------- helper ----------
def read_audio(path, sr=16000):
    y, _ = librosa.load(path, sr=sr, mono=True)
    return y, sr

def transcribe(audio_array, sr=16000):
    inputs = processor.feature_extractor(audio_array, sampling_rate=sr, return_tensors="pt")
    input_values = inputs.input_features.to(DEVICE)
    with torch.no_grad():
        generated_ids = model.generate(input_values)
    transcription = processor.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
    return transcription

# ---------- ground-truth list ----------
# Define in the same order as files sorted by name
ground_truths = [
    "this is the first reference",
    "another ground truth example",
    "more text here"
    # add as many as your test-data files
]

# ---------- collect audio files ----------
audio_files = sorted([os.path.join(AUDIO_DIR, f) 
                      for f in os.listdir(AUDIO_DIR) if f.endswith((".wav", ".mp3", ".flac"))])

if len(audio_files) != len(ground_truths):
    raise ValueError(f"Number of audio files ({len(audio_files)}) != number of ground truths ({len(ground_truths)})")

# ---------- evaluation ----------
results = []
total_proc_time = 0.0
total_audio_duration = 0.0
word_errors, char_errors, sent_errors = [], [], []

for idx, (audio_path, gt) in enumerate(zip(audio_files, ground_truths)):
    y, sr = read_audio(audio_path, sr=16000)
    duration = len(y) / sr

    start = time.time()
    hyp = transcribe(y, sr)
    end = time.time()

    proc_time = end - start
    rtf = proc_time / duration if duration > 0 else float("inf")

    ref_norm = normalization(gt)
    hyp_norm = normalization(hyp)

    w = wer(ref_norm, hyp_norm)
    c = cer(ref_norm, hyp_norm)
    s = 1.0 if ref_norm.strip() != hyp_norm.strip() else 0.0

    results.append({
        "audio": os.path.basename(audio_path),
        "reference": gt,
        "prediction": hyp,
        "wer": w,
        "cer": c,
        "ser": s,
        "duration_s": duration,
        "proc_time_s": proc_time,
        "rtf": rtf
    })

    word_errors.append(w)
    char_errors.append(c)
    sent_errors.append(s)
    total_proc_time += proc_time
    total_audio_duration += duration

    print(f"[{os.path.basename(audio_path)}] WER={w:.3f} CER={c:.3f} SER={s} RTF={rtf:.3f}")

# ---------- save per-audio results ----------
with open(RESULTS_CSV, "w", newline='', encoding="utf-8") as f:
    writer = csv.DictWriter(f, fieldnames=results[0].keys())
    writer.writeheader()
    writer.writerows(results)

# ---------- compute global metrics ----------
avg_wer = float(np.mean(word_errors))
avg_cer = float(np.mean(char_errors))
avg_ser = float(np.mean(sent_errors))
overall_rtf = total_proc_time / total_audio_duration if total_audio_duration > 0 else float("inf")

with open(METRICS_TXT, "w", encoding="utf-8") as f:
    f.write("=== METRICS SUMMARY ===\n")
    f.write(f"Samples: {len(results)}\n")
    f.write(f"Average WER: {avg_wer:.4f}\n")
    f.write(f"Average CER: {avg_cer:.4f}\n")
    f.write(f"Average SER: {avg_ser:.4f}\n")
    f.write(f"Total audio duration (s): {total_audio_duration:.2f}\n")
    f.write(f"Total processing time (s): {total_proc_time:.2f}\n")
    f.write(f"Overall RTF: {overall_rtf:.4f}\n")

print("\nDone! Results saved to:")
print(f" - {RESULTS_CSV}")
print(f" - {METRICS_TXT}")
