{"best_global_step": 200, "best_metric": 1.0804255319148937, "best_model_checkpoint": "./whisper-tiny-ar-quran/checkpoint-200", "epoch": 1.3670999551770506, "eval_steps": 50, "global_step": 6100, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.005602868668758404, "grad_norm": 21.66828155517578, "learning_rate": 4.4e-06, "loss": 4.3294, "step": 25}, {"epoch": 0.011205737337516808, "grad_norm": 15.695514678955078, "learning_rate": 9.4e-06, "loss": 3.7954, "step": 50}, {"epoch": 0.011205737337516808, "eval_loss": 3.415271043777466, "eval_runtime": 221.408, "eval_samples_per_second": 5.844, "eval_steps_per_second": 0.732, "eval_wer": 1.184822695035461, "step": 50}, {"epoch": 0.016808606006275213, "grad_norm": 8.252846717834473, "learning_rate": 9.995063944357191e-06, "loss": 3.2702, "step": 75}, {"epoch": 0.022411474675033616, "grad_norm": 9.971335411071777, "learning_rate": 9.989454790217637e-06, "loss": 2.8812, "step": 100}, {"epoch": 0.022411474675033616, "eval_loss": 2.6263279914855957, "eval_runtime": 212.0523, "eval_samples_per_second": 6.102, "eval_steps_per_second": 0.764, "eval_wer": 1.1092198581560284, "step": 100}, {"epoch": 0.028014343343792023, "grad_norm": 6.732166767120361, "learning_rate": 9.98384563607808e-06, "loss": 2.4738, "step": 125}, {"epoch": 0.033617212012550426, "grad_norm": 5.674781799316406, "learning_rate": 9.978236481938525e-06, "loss": 2.153, "step": 150}, {"epoch": 0.033617212012550426, "eval_loss": 2.0279593467712402, "eval_runtime": 211.6211, "eval_samples_per_second": 6.115, "eval_steps_per_second": 0.766, "eval_wer": 1.1053900709219857, "step": 150}, {"epoch": 0.03922008068130883, "grad_norm": 4.94719934463501, "learning_rate": 9.972627327798968e-06, "loss": 2.0401, "step": 175}, {"epoch": 0.04482294935006723, "grad_norm": 4.771636962890625, "learning_rate": 9.967018173659414e-06, "loss": 1.8955, "step": 200}, {"epoch": 0.04482294935006723, "eval_loss": 1.7179163694381714, "eval_runtime": 208.6924, "eval_samples_per_second": 6.201, "eval_steps_per_second": 0.776, "eval_wer": 1.0804255319148937, "step": 200}, {"epoch": 0.05042581801882564, "grad_norm": 5.264118671417236, "learning_rate": 9.961409019519858e-06, "loss": 1.7472, "step": 225}, {"epoch": 0.056028686687584046, "grad_norm": 5.7515411376953125, "learning_rate": 9.955799865380302e-06, "loss": 1.5907, "step": 250}, {"epoch": 0.056028686687584046, "eval_loss": 1.4611588716506958, "eval_runtime": 209.3846, "eval_samples_per_second": 6.18, "eval_steps_per_second": 0.774, "eval_wer": 1.087659574468085, "step": 250}, {"epoch": 0.061631555356342446, "grad_norm": 4.598886013031006, "learning_rate": 9.950190711240746e-06, "loss": 1.5614, "step": 275}, {"epoch": 0.06723442402510085, "grad_norm": 5.258568286895752, "learning_rate": 9.944581557101189e-06, "loss": 1.4236, "step": 300}, {"epoch": 0.06723442402510085, "eval_loss": 1.3491466045379639, "eval_runtime": 209.5058, "eval_samples_per_second": 6.176, "eval_steps_per_second": 0.773, "eval_wer": 1.1068085106382979, "step": 300}, {"epoch": 0.07283729269385926, "grad_norm": 4.5414252281188965, "learning_rate": 9.938972402961634e-06, "loss": 1.4269, "step": 325}, {"epoch": 0.07844016136261767, "grad_norm": 5.636274814605713, "learning_rate": 9.933363248822079e-06, "loss": 1.3693, "step": 350}, {"epoch": 0.07844016136261767, "eval_loss": 1.2832280397415161, "eval_runtime": 213.9041, "eval_samples_per_second": 6.049, "eval_steps_per_second": 0.757, "eval_wer": 1.1493617021276596, "step": 350}, {"epoch": 0.08404303003137606, "grad_norm": 5.670584201812744, "learning_rate": 9.927754094682523e-06, "loss": 1.3714, "step": 375}, {"epoch": 0.08964589870013447, "grad_norm": 5.561589241027832, "learning_rate": 9.922144940542967e-06, "loss": 1.3242, "step": 400}, {"epoch": 0.08964589870013447, "eval_loss": 1.2357169389724731, "eval_runtime": 214.9444, "eval_samples_per_second": 6.02, "eval_steps_per_second": 0.754, "eval_wer": 1.1561702127659574, "step": 400}, {"epoch": 0.09524876736889287, "grad_norm": 5.977926254272461, "learning_rate": 9.916535786403411e-06, "loss": 1.3296, "step": 425}, {"epoch": 0.10085163603765128, "grad_norm": 5.053528308868408, "learning_rate": 9.910926632263855e-06, "loss": 1.2951, "step": 450}, {"epoch": 0.10085163603765128, "eval_loss": 1.1943706274032593, "eval_runtime": 220.7692, "eval_samples_per_second": 5.861, "eval_steps_per_second": 0.734, "eval_wer": 1.1791489361702128, "step": 450}, {"epoch": 0.10645450470640969, "grad_norm": 5.188421726226807, "learning_rate": 9.9053174781243e-06, "loss": 1.2963, "step": 475}, {"epoch": 0.11205737337516809, "grad_norm": 5.239551544189453, "learning_rate": 9.899708323984744e-06, "loss": 1.1962, "step": 500}, {"epoch": 0.11205737337516809, "eval_loss": 1.1629992723464966, "eval_runtime": 225.2613, "eval_samples_per_second": 5.744, "eval_steps_per_second": 0.719, "eval_wer": 1.1921985815602838, "step": 500}, {"epoch": 0.11766024204392649, "grad_norm": 5.004764556884766, "learning_rate": 9.894099169845188e-06, "loss": 1.2832, "step": 525}, {"epoch": 0.12326311071268489, "grad_norm": 4.031567096710205, "learning_rate": 9.888490015705634e-06, "loss": 1.2088, "step": 550}, {"epoch": 0.12326311071268489, "eval_loss": 1.139546513557434, "eval_runtime": 222.4288, "eval_samples_per_second": 5.818, "eval_steps_per_second": 0.728, "eval_wer": 1.185531914893617, "step": 550}, {"epoch": 0.12886597938144329, "grad_norm": 4.361426830291748, "learning_rate": 9.882880861566076e-06, "loss": 1.2328, "step": 575}, {"epoch": 0.1344688480502017, "grad_norm": 4.530155181884766, "learning_rate": 9.877271707426522e-06, "loss": 1.247, "step": 600}, {"epoch": 0.1344688480502017, "eval_loss": 1.1176620721817017, "eval_runtime": 219.6202, "eval_samples_per_second": 5.892, "eval_steps_per_second": 0.738, "eval_wer": 1.1851063829787234, "step": 600}, {"epoch": 0.1400717167189601, "grad_norm": 4.425103187561035, "learning_rate": 9.871662553286964e-06, "loss": 1.1838, "step": 625}, {"epoch": 0.14567458538771852, "grad_norm": 7.051347732543945, "learning_rate": 9.866053399147409e-06, "loss": 1.1802, "step": 650}, {"epoch": 0.14567458538771852, "eval_loss": 1.096627116203308, "eval_runtime": 225.0399, "eval_samples_per_second": 5.75, "eval_steps_per_second": 0.72, "eval_wer": 1.2059574468085106, "step": 650}, {"epoch": 0.1512774540564769, "grad_norm": 7.999789714813232, "learning_rate": 9.860444245007854e-06, "loss": 1.1883, "step": 675}, {"epoch": 0.15688032272523533, "grad_norm": 4.709761142730713, "learning_rate": 9.854835090868297e-06, "loss": 1.2053, "step": 700}, {"epoch": 0.15688032272523533, "eval_loss": 1.0831581354141235, "eval_runtime": 224.3107, "eval_samples_per_second": 5.769, "eval_steps_per_second": 0.722, "eval_wer": 1.174468085106383, "step": 700}, {"epoch": 0.16248319139399373, "grad_norm": 5.6939377784729, "learning_rate": 9.849225936728743e-06, "loss": 1.1808, "step": 725}, {"epoch": 0.16808606006275212, "grad_norm": 5.018579006195068, "learning_rate": 9.843616782589185e-06, "loss": 1.1532, "step": 750}, {"epoch": 0.16808606006275212, "eval_loss": 1.0685526132583618, "eval_runtime": 220.5661, "eval_samples_per_second": 5.867, "eval_steps_per_second": 0.734, "eval_wer": 1.182127659574468, "step": 750}, {"epoch": 0.17368892873151054, "grad_norm": 4.20207405090332, "learning_rate": 9.838007628449631e-06, "loss": 1.2195, "step": 775}, {"epoch": 0.17929179740026893, "grad_norm": 4.564227104187012, "learning_rate": 9.832398474310075e-06, "loss": 1.125, "step": 800}, {"epoch": 0.17929179740026893, "eval_loss": 1.0584956407546997, "eval_runtime": 221.3888, "eval_samples_per_second": 5.845, "eval_steps_per_second": 0.732, "eval_wer": 1.1767375886524822, "step": 800}, {"epoch": 0.18489466606902735, "grad_norm": 4.685978889465332, "learning_rate": 9.82678932017052e-06, "loss": 1.1425, "step": 825}, {"epoch": 0.19049753473778575, "grad_norm": 3.899707555770874, "learning_rate": 9.821180166030964e-06, "loss": 1.0472, "step": 850}, {"epoch": 0.19049753473778575, "eval_loss": 1.0468710660934448, "eval_runtime": 222.3975, "eval_samples_per_second": 5.818, "eval_steps_per_second": 0.728, "eval_wer": 1.153900709219858, "step": 850}, {"epoch": 0.19610040340654414, "grad_norm": 4.612661361694336, "learning_rate": 9.815571011891408e-06, "loss": 1.0777, "step": 875}, {"epoch": 0.20170327207530256, "grad_norm": 4.733088970184326, "learning_rate": 9.809961857751852e-06, "loss": 1.1139, "step": 900}, {"epoch": 0.20170327207530256, "eval_loss": 1.038216471672058, "eval_runtime": 231.8128, "eval_samples_per_second": 5.582, "eval_steps_per_second": 0.699, "eval_wer": 1.230354609929078, "step": 900}, {"epoch": 0.20730614074406095, "grad_norm": 4.759552478790283, "learning_rate": 9.804352703612296e-06, "loss": 1.0886, "step": 925}, {"epoch": 0.21290900941281937, "grad_norm": 5.37410831451416, "learning_rate": 9.79874354947274e-06, "loss": 1.1455, "step": 950}, {"epoch": 0.21290900941281937, "eval_loss": 1.0290058851242065, "eval_runtime": 227.4348, "eval_samples_per_second": 5.69, "eval_steps_per_second": 0.712, "eval_wer": 1.1887943262411347, "step": 950}, {"epoch": 0.21851187808157777, "grad_norm": 5.273813247680664, "learning_rate": 9.793134395333184e-06, "loss": 1.1234, "step": 975}, {"epoch": 0.22411474675033619, "grad_norm": 4.555939674377441, "learning_rate": 9.787525241193629e-06, "loss": 1.0803, "step": 1000}, {"epoch": 0.22411474675033619, "eval_loss": 1.018477439880371, "eval_runtime": 232.0874, "eval_samples_per_second": 5.575, "eval_steps_per_second": 0.698, "eval_wer": 1.2280851063829787, "step": 1000}, {"epoch": 0.22971761541909458, "grad_norm": 4.7903032302856445, "learning_rate": 9.781916087054073e-06, "loss": 1.073, "step": 1025}, {"epoch": 0.23532048408785297, "grad_norm": 5.021857738494873, "learning_rate": 9.776306932914517e-06, "loss": 1.0927, "step": 1050}, {"epoch": 0.23532048408785297, "eval_loss": 1.0102659463882446, "eval_runtime": 239.9519, "eval_samples_per_second": 5.393, "eval_steps_per_second": 0.675, "eval_wer": 1.2266666666666666, "step": 1050}, {"epoch": 0.2409233527566114, "grad_norm": 6.086860179901123, "learning_rate": 9.770697778774961e-06, "loss": 1.0474, "step": 1075}, {"epoch": 0.24652622142536978, "grad_norm": 4.241894721984863, "learning_rate": 9.765088624635405e-06, "loss": 1.1097, "step": 1100}, {"epoch": 0.24652622142536978, "eval_loss": 1.0039139986038208, "eval_runtime": 242.3991, "eval_samples_per_second": 5.338, "eval_steps_per_second": 0.668, "eval_wer": 1.2245390070921987, "step": 1100}, {"epoch": 0.2521290900941282, "grad_norm": 4.411904335021973, "learning_rate": 9.75947947049585e-06, "loss": 1.0644, "step": 1125}, {"epoch": 0.25773195876288657, "grad_norm": 5.014682769775391, "learning_rate": 9.753870316356294e-06, "loss": 1.1051, "step": 1150}, {"epoch": 0.25773195876288657, "eval_loss": 0.9967908263206482, "eval_runtime": 243.457, "eval_samples_per_second": 5.315, "eval_steps_per_second": 0.665, "eval_wer": 1.24822695035461, "step": 1150}, {"epoch": 0.263334827431645, "grad_norm": 6.69362735748291, "learning_rate": 9.74826116221674e-06, "loss": 1.0559, "step": 1175}, {"epoch": 0.2689376961004034, "grad_norm": 4.867384910583496, "learning_rate": 9.742652008077182e-06, "loss": 1.0408, "step": 1200}, {"epoch": 0.2689376961004034, "eval_loss": 0.9916914701461792, "eval_runtime": 240.1198, "eval_samples_per_second": 5.389, "eval_steps_per_second": 0.675, "eval_wer": 1.2476595744680852, "step": 1200}, {"epoch": 0.27454056476916183, "grad_norm": 5.553133010864258, "learning_rate": 9.737042853937628e-06, "loss": 1.0692, "step": 1225}, {"epoch": 0.2801434334379202, "grad_norm": 5.849860191345215, "learning_rate": 9.73143369979807e-06, "loss": 1.1132, "step": 1250}, {"epoch": 0.2801434334379202, "eval_loss": 0.9846915006637573, "eval_runtime": 244.0297, "eval_samples_per_second": 5.303, "eval_steps_per_second": 0.664, "eval_wer": 1.2514893617021277, "step": 1250}, {"epoch": 0.2857463021066786, "grad_norm": 4.721263885498047, "learning_rate": 9.725824545658516e-06, "loss": 1.0794, "step": 1275}, {"epoch": 0.29134917077543704, "grad_norm": 3.762869119644165, "learning_rate": 9.72021539151896e-06, "loss": 1.0191, "step": 1300}, {"epoch": 0.29134917077543704, "eval_loss": 0.9773968458175659, "eval_runtime": 244.7584, "eval_samples_per_second": 5.287, "eval_steps_per_second": 0.662, "eval_wer": 1.2163120567375887, "step": 1300}, {"epoch": 0.2969520394441954, "grad_norm": 3.888068199157715, "learning_rate": 9.714606237379404e-06, "loss": 1.0437, "step": 1325}, {"epoch": 0.3025549081129538, "grad_norm": 4.676882743835449, "learning_rate": 9.708997083239849e-06, "loss": 1.086, "step": 1350}, {"epoch": 0.3025549081129538, "eval_loss": 0.9730719923973083, "eval_runtime": 253.9832, "eval_samples_per_second": 5.095, "eval_steps_per_second": 0.638, "eval_wer": 1.2703546099290781, "step": 1350}, {"epoch": 0.30815777678171224, "grad_norm": 4.450016021728516, "learning_rate": 9.703387929100291e-06, "loss": 1.0432, "step": 1375}, {"epoch": 0.31376064545047067, "grad_norm": 5.001901149749756, "learning_rate": 9.697778774960737e-06, "loss": 1.0633, "step": 1400}, {"epoch": 0.31376064545047067, "eval_loss": 0.9678431153297424, "eval_runtime": 254.9043, "eval_samples_per_second": 5.076, "eval_steps_per_second": 0.636, "eval_wer": 1.253758865248227, "step": 1400}, {"epoch": 0.31936351411922903, "grad_norm": 4.675822734832764, "learning_rate": 9.692169620821181e-06, "loss": 1.0657, "step": 1425}, {"epoch": 0.32496638278798745, "grad_norm": 5.644407272338867, "learning_rate": 9.686560466681625e-06, "loss": 1.0456, "step": 1450}, {"epoch": 0.32496638278798745, "eval_loss": 0.9590322971343994, "eval_runtime": 249.7384, "eval_samples_per_second": 5.181, "eval_steps_per_second": 0.649, "eval_wer": 1.2565957446808511, "step": 1450}, {"epoch": 0.33056925145674587, "grad_norm": 4.543093681335449, "learning_rate": 9.68095131254207e-06, "loss": 0.9923, "step": 1475}, {"epoch": 0.33617212012550424, "grad_norm": 3.8909969329833984, "learning_rate": 9.675342158402514e-06, "loss": 1.0543, "step": 1500}, {"epoch": 0.33617212012550424, "eval_loss": 0.954785168170929, "eval_runtime": 253.4094, "eval_samples_per_second": 5.106, "eval_steps_per_second": 0.639, "eval_wer": 1.268936170212766, "step": 1500}, {"epoch": 0.34177498879426266, "grad_norm": 5.295046329498291, "learning_rate": 9.669733004262958e-06, "loss": 1.0687, "step": 1525}, {"epoch": 0.3473778574630211, "grad_norm": 5.280679702758789, "learning_rate": 9.664123850123402e-06, "loss": 1.0666, "step": 1550}, {"epoch": 0.3473778574630211, "eval_loss": 0.950537383556366, "eval_runtime": 249.2426, "eval_samples_per_second": 5.192, "eval_steps_per_second": 0.65, "eval_wer": 1.2584397163120566, "step": 1550}, {"epoch": 0.3529807261317795, "grad_norm": 5.625082015991211, "learning_rate": 9.658514695983846e-06, "loss": 1.0261, "step": 1575}, {"epoch": 0.35858359480053786, "grad_norm": 4.591785430908203, "learning_rate": 9.65290554184429e-06, "loss": 0.9961, "step": 1600}, {"epoch": 0.35858359480053786, "eval_loss": 0.9452564716339111, "eval_runtime": 256.1096, "eval_samples_per_second": 5.053, "eval_steps_per_second": 0.633, "eval_wer": 1.3079432624113476, "step": 1600}, {"epoch": 0.3641864634692963, "grad_norm": 6.3105926513671875, "learning_rate": 9.647296387704736e-06, "loss": 1.0426, "step": 1625}, {"epoch": 0.3697893321380547, "grad_norm": 4.842342853546143, "learning_rate": 9.641687233565179e-06, "loss": 1.0171, "step": 1650}, {"epoch": 0.3697893321380547, "eval_loss": 0.9442054629325867, "eval_runtime": 258.4111, "eval_samples_per_second": 5.008, "eval_steps_per_second": 0.627, "eval_wer": 1.2556028368794325, "step": 1650}, {"epoch": 0.37539220080681307, "grad_norm": 5.009239196777344, "learning_rate": 9.636078079425624e-06, "loss": 0.9593, "step": 1675}, {"epoch": 0.3809950694755715, "grad_norm": 5.40683126449585, "learning_rate": 9.630468925286067e-06, "loss": 0.9691, "step": 1700}, {"epoch": 0.3809950694755715, "eval_loss": 0.9397484064102173, "eval_runtime": 257.8728, "eval_samples_per_second": 5.018, "eval_steps_per_second": 0.628, "eval_wer": 1.2703546099290781, "step": 1700}, {"epoch": 0.3865979381443299, "grad_norm": 6.0612897872924805, "learning_rate": 9.624859771146511e-06, "loss": 1.0098, "step": 1725}, {"epoch": 0.3922008068130883, "grad_norm": 5.429316997528076, "learning_rate": 9.619250617006957e-06, "loss": 0.9759, "step": 1750}, {"epoch": 0.3922008068130883, "eval_loss": 0.9333999156951904, "eval_runtime": 255.1647, "eval_samples_per_second": 5.071, "eval_steps_per_second": 0.635, "eval_wer": 1.2405673758865248, "step": 1750}, {"epoch": 0.3978036754818467, "grad_norm": 4.616994380950928, "learning_rate": 9.6136414628674e-06, "loss": 0.9912, "step": 1775}, {"epoch": 0.4034065441506051, "grad_norm": 5.299432754516602, "learning_rate": 9.608032308727845e-06, "loss": 1.012, "step": 1800}, {"epoch": 0.4034065441506051, "eval_loss": 0.9290720820426941, "eval_runtime": 264.9378, "eval_samples_per_second": 4.884, "eval_steps_per_second": 0.611, "eval_wer": 1.325390070921986, "step": 1800}, {"epoch": 0.40900941281936354, "grad_norm": 5.081997871398926, "learning_rate": 9.602423154588288e-06, "loss": 1.0224, "step": 1825}, {"epoch": 0.4146122814881219, "grad_norm": 4.3880534172058105, "learning_rate": 9.596814000448734e-06, "loss": 0.9918, "step": 1850}, {"epoch": 0.4146122814881219, "eval_loss": 0.9260596632957458, "eval_runtime": 262.0163, "eval_samples_per_second": 4.939, "eval_steps_per_second": 0.618, "eval_wer": 1.3028368794326242, "step": 1850}, {"epoch": 0.4202151501568803, "grad_norm": 5.280064582824707, "learning_rate": 9.591204846309178e-06, "loss": 1.0026, "step": 1875}, {"epoch": 0.42581801882563874, "grad_norm": 5.181178092956543, "learning_rate": 9.585595692169622e-06, "loss": 0.9743, "step": 1900}, {"epoch": 0.42581801882563874, "eval_loss": 0.9214015007019043, "eval_runtime": 253.9623, "eval_samples_per_second": 5.095, "eval_steps_per_second": 0.638, "eval_wer": 1.2401418439716312, "step": 1900}, {"epoch": 0.4314208874943971, "grad_norm": 5.052783966064453, "learning_rate": 9.579986538030066e-06, "loss": 0.9517, "step": 1925}, {"epoch": 0.43702375616315553, "grad_norm": 5.041476726531982, "learning_rate": 9.57437738389051e-06, "loss": 0.9553, "step": 1950}, {"epoch": 0.43702375616315553, "eval_loss": 0.9173444509506226, "eval_runtime": 256.6351, "eval_samples_per_second": 5.042, "eval_steps_per_second": 0.631, "eval_wer": 1.246950354609929, "step": 1950}, {"epoch": 0.44262662483191395, "grad_norm": 4.986549377441406, "learning_rate": 9.568768229750954e-06, "loss": 0.9857, "step": 1975}, {"epoch": 0.44822949350067237, "grad_norm": 5.659454822540283, "learning_rate": 9.563159075611399e-06, "loss": 0.9822, "step": 2000}, {"epoch": 0.44822949350067237, "eval_loss": 0.9151190519332886, "eval_runtime": 264.7342, "eval_samples_per_second": 4.888, "eval_steps_per_second": 0.612, "eval_wer": 1.3024113475177306, "step": 2000}, {"epoch": 0.45383236216943074, "grad_norm": 6.27190637588501, "learning_rate": 9.557549921471843e-06, "loss": 1.0097, "step": 2025}, {"epoch": 0.45943523083818916, "grad_norm": 5.278228759765625, "learning_rate": 9.551940767332287e-06, "loss": 0.9419, "step": 2050}, {"epoch": 0.45943523083818916, "eval_loss": 0.9094217419624329, "eval_runtime": 255.5297, "eval_samples_per_second": 5.064, "eval_steps_per_second": 0.634, "eval_wer": 1.2859574468085107, "step": 2050}, {"epoch": 0.4650380995069476, "grad_norm": 5.5756378173828125, "learning_rate": 9.546331613192731e-06, "loss": 0.9757, "step": 2075}, {"epoch": 0.47064096817570594, "grad_norm": 6.034989356994629, "learning_rate": 9.540722459053175e-06, "loss": 0.972, "step": 2100}, {"epoch": 0.47064096817570594, "eval_loss": 0.9069719910621643, "eval_runtime": 258.7344, "eval_samples_per_second": 5.001, "eval_steps_per_second": 0.626, "eval_wer": 1.2470921985815602, "step": 2100}, {"epoch": 0.47624383684446436, "grad_norm": 4.5674214363098145, "learning_rate": 9.53511330491362e-06, "loss": 0.9973, "step": 2125}, {"epoch": 0.4818467055132228, "grad_norm": 5.538179397583008, "learning_rate": 9.529504150774064e-06, "loss": 0.9523, "step": 2150}, {"epoch": 0.4818467055132228, "eval_loss": 0.903247058391571, "eval_runtime": 257.2622, "eval_samples_per_second": 5.03, "eval_steps_per_second": 0.63, "eval_wer": 1.2278014184397164, "step": 2150}, {"epoch": 0.48744957418198115, "grad_norm": 4.609229564666748, "learning_rate": 9.523894996634508e-06, "loss": 0.9962, "step": 2175}, {"epoch": 0.49305244285073957, "grad_norm": 3.7787420749664307, "learning_rate": 9.518285842494952e-06, "loss": 0.9178, "step": 2200}, {"epoch": 0.49305244285073957, "eval_loss": 0.9013031125068665, "eval_runtime": 265.5594, "eval_samples_per_second": 4.873, "eval_steps_per_second": 0.61, "eval_wer": 1.2523404255319148, "step": 2200}, {"epoch": 0.498655311519498, "grad_norm": 5.7187652587890625, "learning_rate": 9.512676688355396e-06, "loss": 0.9814, "step": 2225}, {"epoch": 0.5042581801882564, "grad_norm": 4.674436092376709, "learning_rate": 9.507067534215842e-06, "loss": 0.9877, "step": 2250}, {"epoch": 0.5042581801882564, "eval_loss": 0.8975381851196289, "eval_runtime": 256.2123, "eval_samples_per_second": 5.05, "eval_steps_per_second": 0.632, "eval_wer": 1.22354609929078, "step": 2250}, {"epoch": 0.5098610488570148, "grad_norm": 4.5930399894714355, "learning_rate": 9.501458380076284e-06, "loss": 0.9295, "step": 2275}, {"epoch": 0.5154639175257731, "grad_norm": 5.782462120056152, "learning_rate": 9.49584922593673e-06, "loss": 0.9888, "step": 2300}, {"epoch": 0.5154639175257731, "eval_loss": 0.8968179821968079, "eval_runtime": 266.6763, "eval_samples_per_second": 4.852, "eval_steps_per_second": 0.607, "eval_wer": 1.2460992907801418, "step": 2300}, {"epoch": 0.5210667861945316, "grad_norm": 5.186969757080078, "learning_rate": 9.490240071797173e-06, "loss": 0.9047, "step": 2325}, {"epoch": 0.52666965486329, "grad_norm": 5.373775959014893, "learning_rate": 9.484630917657619e-06, "loss": 0.9821, "step": 2350}, {"epoch": 0.52666965486329, "eval_loss": 0.8949685096740723, "eval_runtime": 266.6227, "eval_samples_per_second": 4.853, "eval_steps_per_second": 0.608, "eval_wer": 1.218014184397163, "step": 2350}, {"epoch": 0.5322725235320485, "grad_norm": 5.610340118408203, "learning_rate": 9.479021763518063e-06, "loss": 0.9498, "step": 2375}, {"epoch": 0.5378753922008068, "grad_norm": 5.825931072235107, "learning_rate": 9.473412609378507e-06, "loss": 0.9923, "step": 2400}, {"epoch": 0.5378753922008068, "eval_loss": 0.8906533718109131, "eval_runtime": 264.9991, "eval_samples_per_second": 4.883, "eval_steps_per_second": 0.611, "eval_wer": 1.2727659574468084, "step": 2400}, {"epoch": 0.5434782608695652, "grad_norm": 3.9542229175567627, "learning_rate": 9.467803455238951e-06, "loss": 0.9793, "step": 2425}, {"epoch": 0.5490811295383237, "grad_norm": 4.759235858917236, "learning_rate": 9.462194301099395e-06, "loss": 0.9691, "step": 2450}, {"epoch": 0.5490811295383237, "eval_loss": 0.8861823081970215, "eval_runtime": 264.7335, "eval_samples_per_second": 4.888, "eval_steps_per_second": 0.612, "eval_wer": 1.267517730496454, "step": 2450}, {"epoch": 0.554683998207082, "grad_norm": 5.861250877380371, "learning_rate": 9.45658514695984e-06, "loss": 0.9022, "step": 2475}, {"epoch": 0.5602868668758404, "grad_norm": 6.541745662689209, "learning_rate": 9.450975992820284e-06, "loss": 0.9708, "step": 2500}, {"epoch": 0.5602868668758404, "eval_loss": 0.8835633993148804, "eval_runtime": 273.0818, "eval_samples_per_second": 4.739, "eval_steps_per_second": 0.593, "eval_wer": 1.2673758865248228, "step": 2500}, {"epoch": 0.5658897355445989, "grad_norm": 4.371872901916504, "learning_rate": 9.445366838680728e-06, "loss": 0.9149, "step": 2525}, {"epoch": 0.5714926042133572, "grad_norm": 6.39174222946167, "learning_rate": 9.439757684541172e-06, "loss": 0.9454, "step": 2550}, {"epoch": 0.5714926042133572, "eval_loss": 0.8795706629753113, "eval_runtime": 275.1803, "eval_samples_per_second": 4.702, "eval_steps_per_second": 0.589, "eval_wer": 1.2872340425531914, "step": 2550}, {"epoch": 0.5770954728821156, "grad_norm": 5.3186774253845215, "learning_rate": 9.434148530401616e-06, "loss": 0.9418, "step": 2575}, {"epoch": 0.5826983415508741, "grad_norm": 4.716335296630859, "learning_rate": 9.42853937626206e-06, "loss": 0.937, "step": 2600}, {"epoch": 0.5826983415508741, "eval_loss": 0.877787172794342, "eval_runtime": 279.9041, "eval_samples_per_second": 4.623, "eval_steps_per_second": 0.579, "eval_wer": 1.3319148936170213, "step": 2600}, {"epoch": 0.5883012102196324, "grad_norm": 5.251962184906006, "learning_rate": 9.422930222122504e-06, "loss": 0.988, "step": 2625}, {"epoch": 0.5939040788883908, "grad_norm": 5.506538391113281, "learning_rate": 9.417321067982949e-06, "loss": 0.9388, "step": 2650}, {"epoch": 0.5939040788883908, "eval_loss": 0.8750694990158081, "eval_runtime": 270.3555, "eval_samples_per_second": 4.786, "eval_steps_per_second": 0.599, "eval_wer": 1.281985815602837, "step": 2650}, {"epoch": 0.5995069475571493, "grad_norm": 5.68796968460083, "learning_rate": 9.411711913843393e-06, "loss": 0.9612, "step": 2675}, {"epoch": 0.6051098162259076, "grad_norm": 5.644659996032715, "learning_rate": 9.406102759703839e-06, "loss": 0.8931, "step": 2700}, {"epoch": 0.6051098162259076, "eval_loss": 0.8732804656028748, "eval_runtime": 285.916, "eval_samples_per_second": 4.526, "eval_steps_per_second": 0.567, "eval_wer": 1.3391489361702127, "step": 2700}, {"epoch": 0.610712684894666, "grad_norm": 5.842566013336182, "learning_rate": 9.400493605564281e-06, "loss": 0.887, "step": 2725}, {"epoch": 0.6163155535634245, "grad_norm": 5.366817951202393, "learning_rate": 9.394884451424727e-06, "loss": 0.9039, "step": 2750}, {"epoch": 0.6163155535634245, "eval_loss": 0.870310366153717, "eval_runtime": 283.8492, "eval_samples_per_second": 4.559, "eval_steps_per_second": 0.571, "eval_wer": 1.3336170212765956, "step": 2750}, {"epoch": 0.6219184222321829, "grad_norm": 5.609750270843506, "learning_rate": 9.38927529728517e-06, "loss": 0.995, "step": 2775}, {"epoch": 0.6275212909009413, "grad_norm": 5.635646343231201, "learning_rate": 9.383666143145614e-06, "loss": 0.9239, "step": 2800}, {"epoch": 0.6275212909009413, "eval_loss": 0.8679163455963135, "eval_runtime": 288.8851, "eval_samples_per_second": 4.479, "eval_steps_per_second": 0.561, "eval_wer": 1.3714893617021278, "step": 2800}, {"epoch": 0.6331241595696997, "grad_norm": 6.299793720245361, "learning_rate": 9.37805698900606e-06, "loss": 0.9348, "step": 2825}, {"epoch": 0.6387270282384581, "grad_norm": 5.66321325302124, "learning_rate": 9.372447834866502e-06, "loss": 0.954, "step": 2850}, {"epoch": 0.6387270282384581, "eval_loss": 0.8657387495040894, "eval_runtime": 273.3024, "eval_samples_per_second": 4.735, "eval_steps_per_second": 0.593, "eval_wer": 1.2443971631205675, "step": 2850}, {"epoch": 0.6443298969072165, "grad_norm": 5.234557628631592, "learning_rate": 9.366838680726948e-06, "loss": 0.9745, "step": 2875}, {"epoch": 0.6499327655759749, "grad_norm": 5.924834251403809, "learning_rate": 9.36122952658739e-06, "loss": 0.8929, "step": 2900}, {"epoch": 0.6499327655759749, "eval_loss": 0.8653894066810608, "eval_runtime": 284.3665, "eval_samples_per_second": 4.55, "eval_steps_per_second": 0.57, "eval_wer": 1.324964539007092, "step": 2900}, {"epoch": 0.6555356342447333, "grad_norm": 5.328509330749512, "learning_rate": 9.355620372447836e-06, "loss": 0.9144, "step": 2925}, {"epoch": 0.6611385029134917, "grad_norm": 4.9144392013549805, "learning_rate": 9.35001121830828e-06, "loss": 0.8998, "step": 2950}, {"epoch": 0.6611385029134917, "eval_loss": 0.8622061610221863, "eval_runtime": 278.0805, "eval_samples_per_second": 4.653, "eval_steps_per_second": 0.583, "eval_wer": 1.2750354609929078, "step": 2950}, {"epoch": 0.6667413715822501, "grad_norm": 5.263082981109619, "learning_rate": 9.344402064168724e-06, "loss": 0.9105, "step": 2975}, {"epoch": 0.6723442402510085, "grad_norm": 5.458959579467773, "learning_rate": 9.338792910029169e-06, "loss": 0.9655, "step": 3000}, {"epoch": 0.6723442402510085, "eval_loss": 0.8594318628311157, "eval_runtime": 274.8796, "eval_samples_per_second": 4.708, "eval_steps_per_second": 0.589, "eval_wer": 1.2845390070921985, "step": 3000}, {"epoch": 0.677947108919767, "grad_norm": 5.080052852630615, "learning_rate": 9.333183755889613e-06, "loss": 0.9202, "step": 3025}, {"epoch": 0.6835499775885253, "grad_norm": 5.60837984085083, "learning_rate": 9.327574601750057e-06, "loss": 0.9439, "step": 3050}, {"epoch": 0.6835499775885253, "eval_loss": 0.8575718402862549, "eval_runtime": 284.7009, "eval_samples_per_second": 4.545, "eval_steps_per_second": 0.569, "eval_wer": 1.3343262411347518, "step": 3050}, {"epoch": 0.6891528462572837, "grad_norm": 4.574904441833496, "learning_rate": 9.321965447610501e-06, "loss": 0.9128, "step": 3075}, {"epoch": 0.6947557149260422, "grad_norm": 6.337819576263428, "learning_rate": 9.316356293470945e-06, "loss": 0.8963, "step": 3100}, {"epoch": 0.6947557149260422, "eval_loss": 0.8562303781509399, "eval_runtime": 284.1429, "eval_samples_per_second": 4.554, "eval_steps_per_second": 0.57, "eval_wer": 1.3139007092198582, "step": 3100}, {"epoch": 0.7003585835948005, "grad_norm": 4.599398136138916, "learning_rate": 9.31074713933139e-06, "loss": 0.9277, "step": 3125}, {"epoch": 0.705961452263559, "grad_norm": 4.560904502868652, "learning_rate": 9.305137985191834e-06, "loss": 0.8963, "step": 3150}, {"epoch": 0.705961452263559, "eval_loss": 0.8553858399391174, "eval_runtime": 277.8325, "eval_samples_per_second": 4.657, "eval_steps_per_second": 0.583, "eval_wer": 1.2602836879432624, "step": 3150}, {"epoch": 0.7115643209323174, "grad_norm": 4.397059440612793, "learning_rate": 9.299528831052278e-06, "loss": 0.8667, "step": 3175}, {"epoch": 0.7171671896010757, "grad_norm": 5.682547092437744, "learning_rate": 9.293919676912722e-06, "loss": 0.9481, "step": 3200}, {"epoch": 0.7171671896010757, "eval_loss": 0.8515233397483826, "eval_runtime": 288.2844, "eval_samples_per_second": 4.489, "eval_steps_per_second": 0.562, "eval_wer": 1.3574468085106384, "step": 3200}, {"epoch": 0.7227700582698342, "grad_norm": 4.237405300140381, "learning_rate": 9.288310522773166e-06, "loss": 0.8404, "step": 3225}, {"epoch": 0.7283729269385926, "grad_norm": 5.169107913970947, "learning_rate": 9.28270136863361e-06, "loss": 0.9191, "step": 3250}, {"epoch": 0.7283729269385926, "eval_loss": 0.8483667969703674, "eval_runtime": 286.5107, "eval_samples_per_second": 4.516, "eval_steps_per_second": 0.565, "eval_wer": 1.2853900709219859, "step": 3250}, {"epoch": 0.7339757956073509, "grad_norm": 5.771963596343994, "learning_rate": 9.277092214494054e-06, "loss": 0.8954, "step": 3275}, {"epoch": 0.7395786642761094, "grad_norm": 5.070594787597656, "learning_rate": 9.271483060354499e-06, "loss": 0.9224, "step": 3300}, {"epoch": 0.7395786642761094, "eval_loss": 0.8486400842666626, "eval_runtime": 292.3662, "eval_samples_per_second": 4.426, "eval_steps_per_second": 0.554, "eval_wer": 1.3083687943262412, "step": 3300}, {"epoch": 0.7451815329448678, "grad_norm": 5.607713222503662, "learning_rate": 9.265873906214944e-06, "loss": 0.9396, "step": 3325}, {"epoch": 0.7507844016136261, "grad_norm": 6.357516288757324, "learning_rate": 9.260264752075387e-06, "loss": 0.9074, "step": 3350}, {"epoch": 0.7507844016136261, "eval_loss": 0.8457932472229004, "eval_runtime": 282.5149, "eval_samples_per_second": 4.58, "eval_steps_per_second": 0.573, "eval_wer": 1.2973049645390071, "step": 3350}, {"epoch": 0.7563872702823846, "grad_norm": 5.7871994972229, "learning_rate": 9.254655597935833e-06, "loss": 0.9086, "step": 3375}, {"epoch": 0.761990138951143, "grad_norm": 5.903895854949951, "learning_rate": 9.249046443796277e-06, "loss": 0.8161, "step": 3400}, {"epoch": 0.761990138951143, "eval_loss": 0.8448153138160706, "eval_runtime": 287.5795, "eval_samples_per_second": 4.5, "eval_steps_per_second": 0.563, "eval_wer": 1.2906382978723405, "step": 3400}, {"epoch": 0.7675930076199013, "grad_norm": 5.441049575805664, "learning_rate": 9.243437289656721e-06, "loss": 0.9157, "step": 3425}, {"epoch": 0.7731958762886598, "grad_norm": 5.258452892303467, "learning_rate": 9.237828135517165e-06, "loss": 0.9628, "step": 3450}, {"epoch": 0.7731958762886598, "eval_loss": 0.8417153358459473, "eval_runtime": 289.48, "eval_samples_per_second": 4.47, "eval_steps_per_second": 0.56, "eval_wer": 1.3154609929078014, "step": 3450}, {"epoch": 0.7787987449574182, "grad_norm": 7.237786769866943, "learning_rate": 9.23221898137761e-06, "loss": 0.9453, "step": 3475}, {"epoch": 0.7844016136261766, "grad_norm": 5.123056411743164, "learning_rate": 9.226609827238054e-06, "loss": 0.9021, "step": 3500}, {"epoch": 0.7844016136261766, "eval_loss": 0.8421899080276489, "eval_runtime": 293.1623, "eval_samples_per_second": 4.414, "eval_steps_per_second": 0.553, "eval_wer": 1.337163120567376, "step": 3500}, {"epoch": 0.790004482294935, "grad_norm": 6.3787055015563965, "learning_rate": 9.221000673098498e-06, "loss": 0.8795, "step": 3525}, {"epoch": 0.7956073509636934, "grad_norm": 5.068078517913818, "learning_rate": 9.215391518958942e-06, "loss": 0.8756, "step": 3550}, {"epoch": 0.7956073509636934, "eval_loss": 0.8399507403373718, "eval_runtime": 292.1224, "eval_samples_per_second": 4.43, "eval_steps_per_second": 0.555, "eval_wer": 1.2604255319148936, "step": 3550}, {"epoch": 0.8012102196324519, "grad_norm": 5.639192581176758, "learning_rate": 9.209782364819386e-06, "loss": 0.8604, "step": 3575}, {"epoch": 0.8068130883012102, "grad_norm": 5.845419406890869, "learning_rate": 9.20417321067983e-06, "loss": 0.9131, "step": 3600}, {"epoch": 0.8068130883012102, "eval_loss": 0.8372408151626587, "eval_runtime": 298.6604, "eval_samples_per_second": 4.333, "eval_steps_per_second": 0.542, "eval_wer": 1.339432624113475, "step": 3600}, {"epoch": 0.8124159569699686, "grad_norm": 4.887937545776367, "learning_rate": 9.198564056540274e-06, "loss": 0.8977, "step": 3625}, {"epoch": 0.8180188256387271, "grad_norm": 4.781653881072998, "learning_rate": 9.192954902400719e-06, "loss": 0.8671, "step": 3650}, {"epoch": 0.8180188256387271, "eval_loss": 0.8360865116119385, "eval_runtime": 283.9364, "eval_samples_per_second": 4.557, "eval_steps_per_second": 0.571, "eval_wer": 1.2825531914893618, "step": 3650}, {"epoch": 0.8236216943074854, "grad_norm": 5.225576400756836, "learning_rate": 9.187345748261163e-06, "loss": 0.8684, "step": 3675}, {"epoch": 0.8292245629762438, "grad_norm": 5.623165607452393, "learning_rate": 9.181736594121607e-06, "loss": 0.9044, "step": 3700}, {"epoch": 0.8292245629762438, "eval_loss": 0.8360756039619446, "eval_runtime": 290.4293, "eval_samples_per_second": 4.455, "eval_steps_per_second": 0.558, "eval_wer": 1.3022695035460994, "step": 3700}, {"epoch": 0.8348274316450023, "grad_norm": 4.244664192199707, "learning_rate": 9.176127439982051e-06, "loss": 0.8832, "step": 3725}, {"epoch": 0.8404303003137606, "grad_norm": 4.45029354095459, "learning_rate": 9.170518285842495e-06, "loss": 0.8515, "step": 3750}, {"epoch": 0.8404303003137606, "eval_loss": 0.835564911365509, "eval_runtime": 285.8246, "eval_samples_per_second": 4.527, "eval_steps_per_second": 0.567, "eval_wer": 1.2333333333333334, "step": 3750}, {"epoch": 0.846033168982519, "grad_norm": 4.328171730041504, "learning_rate": 9.164909131702941e-06, "loss": 0.8142, "step": 3775}, {"epoch": 0.8516360376512775, "grad_norm": 5.63730001449585, "learning_rate": 9.159299977563384e-06, "loss": 0.8882, "step": 3800}, {"epoch": 0.8516360376512775, "eval_loss": 0.831829309463501, "eval_runtime": 293.9673, "eval_samples_per_second": 4.402, "eval_steps_per_second": 0.551, "eval_wer": 1.2523404255319148, "step": 3800}, {"epoch": 0.8572389063200359, "grad_norm": 7.662906646728516, "learning_rate": 9.15369082342383e-06, "loss": 0.8857, "step": 3825}, {"epoch": 0.8628417749887942, "grad_norm": 4.17808723449707, "learning_rate": 9.148081669284272e-06, "loss": 0.8914, "step": 3850}, {"epoch": 0.8628417749887942, "eval_loss": 0.8295116424560547, "eval_runtime": 290.772, "eval_samples_per_second": 4.45, "eval_steps_per_second": 0.557, "eval_wer": 1.2278014184397164, "step": 3850}, {"epoch": 0.8684446436575527, "grad_norm": 5.130710601806641, "learning_rate": 9.142472515144716e-06, "loss": 0.8869, "step": 3875}, {"epoch": 0.8740475123263111, "grad_norm": 4.177347660064697, "learning_rate": 9.136863361005162e-06, "loss": 0.8739, "step": 3900}, {"epoch": 0.8740475123263111, "eval_loss": 0.8282884359359741, "eval_runtime": 305.0202, "eval_samples_per_second": 4.242, "eval_steps_per_second": 0.531, "eval_wer": 1.3212765957446808, "step": 3900}, {"epoch": 0.8796503809950694, "grad_norm": 5.734642028808594, "learning_rate": 9.131254206865604e-06, "loss": 0.8699, "step": 3925}, {"epoch": 0.8852532496638279, "grad_norm": 6.135047912597656, "learning_rate": 9.12564505272605e-06, "loss": 0.8869, "step": 3950}, {"epoch": 0.8852532496638279, "eval_loss": 0.8283160328865051, "eval_runtime": 298.5957, "eval_samples_per_second": 4.334, "eval_steps_per_second": 0.543, "eval_wer": 1.3046808510638297, "step": 3950}, {"epoch": 0.8908561183325863, "grad_norm": 6.199937343597412, "learning_rate": 9.120035898586493e-06, "loss": 0.8624, "step": 3975}, {"epoch": 0.8964589870013447, "grad_norm": 5.896580219268799, "learning_rate": 9.114426744446939e-06, "loss": 0.8788, "step": 4000}, {"epoch": 0.8964589870013447, "eval_loss": 0.8280980587005615, "eval_runtime": 314.1493, "eval_samples_per_second": 4.119, "eval_steps_per_second": 0.516, "eval_wer": 1.364113475177305, "step": 4000}, {"epoch": 0.9020618556701031, "grad_norm": 5.867917060852051, "learning_rate": 9.108817590307383e-06, "loss": 0.8363, "step": 4025}, {"epoch": 0.9076647243388615, "grad_norm": 4.607292652130127, "learning_rate": 9.103208436167827e-06, "loss": 0.904, "step": 4050}, {"epoch": 0.9076647243388615, "eval_loss": 0.8240814208984375, "eval_runtime": 305.5103, "eval_samples_per_second": 4.236, "eval_steps_per_second": 0.53, "eval_wer": 1.305390070921986, "step": 4050}, {"epoch": 0.91326759300762, "grad_norm": 5.600480079650879, "learning_rate": 9.097599282028271e-06, "loss": 0.8443, "step": 4075}, {"epoch": 0.9188704616763783, "grad_norm": 5.5143046379089355, "learning_rate": 9.091990127888715e-06, "loss": 0.846, "step": 4100}, {"epoch": 0.9188704616763783, "eval_loss": 0.8219508528709412, "eval_runtime": 305.5858, "eval_samples_per_second": 4.234, "eval_steps_per_second": 0.53, "eval_wer": 1.3017021276595744, "step": 4100}, {"epoch": 0.9244733303451367, "grad_norm": 4.968230724334717, "learning_rate": 9.08638097374916e-06, "loss": 0.9523, "step": 4125}, {"epoch": 0.9300761990138952, "grad_norm": 6.961663246154785, "learning_rate": 9.080771819609604e-06, "loss": 0.8804, "step": 4150}, {"epoch": 0.9300761990138952, "eval_loss": 0.821404755115509, "eval_runtime": 314.6224, "eval_samples_per_second": 4.113, "eval_steps_per_second": 0.515, "eval_wer": 1.3636879432624114, "step": 4150}, {"epoch": 0.9356790676826535, "grad_norm": 5.427481651306152, "learning_rate": 9.075162665470048e-06, "loss": 0.8539, "step": 4175}, {"epoch": 0.9412819363514119, "grad_norm": 6.031769275665283, "learning_rate": 9.069553511330492e-06, "loss": 0.9009, "step": 4200}, {"epoch": 0.9412819363514119, "eval_loss": 0.8183842301368713, "eval_runtime": 307.9763, "eval_samples_per_second": 4.202, "eval_steps_per_second": 0.526, "eval_wer": 1.3795744680851063, "step": 4200}, {"epoch": 0.9468848050201704, "grad_norm": 5.832958698272705, "learning_rate": 9.063944357190936e-06, "loss": 0.8835, "step": 4225}, {"epoch": 0.9524876736889287, "grad_norm": 5.391894817352295, "learning_rate": 9.05833520305138e-06, "loss": 0.9402, "step": 4250}, {"epoch": 0.9524876736889287, "eval_loss": 0.8188268542289734, "eval_runtime": 309.5263, "eval_samples_per_second": 4.181, "eval_steps_per_second": 0.523, "eval_wer": 1.3574468085106384, "step": 4250}, {"epoch": 0.9580905423576871, "grad_norm": 5.502048969268799, "learning_rate": 9.052726048911824e-06, "loss": 0.8586, "step": 4275}, {"epoch": 0.9636934110264456, "grad_norm": 5.172474384307861, "learning_rate": 9.047116894772269e-06, "loss": 0.8807, "step": 4300}, {"epoch": 0.9636934110264456, "eval_loss": 0.8185968399047852, "eval_runtime": 311.5791, "eval_samples_per_second": 4.153, "eval_steps_per_second": 0.52, "eval_wer": 1.3851063829787233, "step": 4300}, {"epoch": 0.9692962796952039, "grad_norm": 6.03965425491333, "learning_rate": 9.041507740632713e-06, "loss": 0.8572, "step": 4325}, {"epoch": 0.9748991483639623, "grad_norm": 6.153266429901123, "learning_rate": 9.035898586493159e-06, "loss": 0.8936, "step": 4350}, {"epoch": 0.9748991483639623, "eval_loss": 0.8139490485191345, "eval_runtime": 312.019, "eval_samples_per_second": 4.147, "eval_steps_per_second": 0.519, "eval_wer": 1.3646808510638297, "step": 4350}, {"epoch": 0.9805020170327208, "grad_norm": 4.015699863433838, "learning_rate": 9.030289432353601e-06, "loss": 0.8104, "step": 4375}, {"epoch": 0.9861048857014791, "grad_norm": 6.01719856262207, "learning_rate": 9.024680278214047e-06, "loss": 0.8556, "step": 4400}, {"epoch": 0.9861048857014791, "eval_loss": 0.8162193894386292, "eval_runtime": 299.7059, "eval_samples_per_second": 4.318, "eval_steps_per_second": 0.541, "eval_wer": 1.2948936170212766, "step": 4400}, {"epoch": 0.9917077543702376, "grad_norm": 6.168245315551758, "learning_rate": 9.01907112407449e-06, "loss": 0.8505, "step": 4425}, {"epoch": 0.997310623038996, "grad_norm": 5.481610298156738, "learning_rate": 9.013461969934935e-06, "loss": 0.8049, "step": 4450}, {"epoch": 0.997310623038996, "eval_loss": 0.8133124709129333, "eval_runtime": 298.6716, "eval_samples_per_second": 4.333, "eval_steps_per_second": 0.542, "eval_wer": 1.2880851063829788, "step": 4450}, {"epoch": 1.0029134917077545, "grad_norm": 6.183061122894287, "learning_rate": 9.00785281579538e-06, "loss": 0.872, "step": 4475}, {"epoch": 1.0085163603765128, "grad_norm": 5.618913173675537, "learning_rate": 9.002243661655824e-06, "loss": 0.8656, "step": 4500}, {"epoch": 1.0085163603765128, "eval_loss": 0.8117003440856934, "eval_runtime": 312.4899, "eval_samples_per_second": 4.141, "eval_steps_per_second": 0.518, "eval_wer": 1.3673758865248227, "step": 4500}, {"epoch": 1.0141192290452712, "grad_norm": 3.79740309715271, "learning_rate": 8.996634507516268e-06, "loss": 0.8867, "step": 4525}, {"epoch": 1.0197220977140296, "grad_norm": 4.241306781768799, "learning_rate": 8.991025353376712e-06, "loss": 0.8677, "step": 4550}, {"epoch": 1.0197220977140296, "eval_loss": 0.8105788826942444, "eval_runtime": 306.333, "eval_samples_per_second": 4.224, "eval_steps_per_second": 0.529, "eval_wer": 1.3526241134751773, "step": 4550}, {"epoch": 1.025324966382788, "grad_norm": 5.857481956481934, "learning_rate": 8.985416199237156e-06, "loss": 0.8711, "step": 4575}, {"epoch": 1.0309278350515463, "grad_norm": 6.188135147094727, "learning_rate": 8.9798070450976e-06, "loss": 0.8062, "step": 4600}, {"epoch": 1.0309278350515463, "eval_loss": 0.8107039928436279, "eval_runtime": 325.3511, "eval_samples_per_second": 3.977, "eval_steps_per_second": 0.498, "eval_wer": 1.4035460992907802, "step": 4600}, {"epoch": 1.0365307037203049, "grad_norm": 6.698085308074951, "learning_rate": 8.974197890958044e-06, "loss": 0.8387, "step": 4625}, {"epoch": 1.0421335723890632, "grad_norm": 7.483396053314209, "learning_rate": 8.968588736818489e-06, "loss": 0.8915, "step": 4650}, {"epoch": 1.0421335723890632, "eval_loss": 0.8113996982574463, "eval_runtime": 324.7485, "eval_samples_per_second": 3.985, "eval_steps_per_second": 0.499, "eval_wer": 1.4438297872340426, "step": 4650}, {"epoch": 1.0477364410578216, "grad_norm": 8.563643455505371, "learning_rate": 8.962979582678933e-06, "loss": 0.8426, "step": 4675}, {"epoch": 1.05333930972658, "grad_norm": 4.686744213104248, "learning_rate": 8.957370428539377e-06, "loss": 0.8328, "step": 4700}, {"epoch": 1.05333930972658, "eval_loss": 0.8079043626785278, "eval_runtime": 311.2859, "eval_samples_per_second": 4.157, "eval_steps_per_second": 0.52, "eval_wer": 1.4022695035460992, "step": 4700}, {"epoch": 1.0589421783953383, "grad_norm": 5.158753871917725, "learning_rate": 8.951761274399821e-06, "loss": 0.8414, "step": 4725}, {"epoch": 1.064545047064097, "grad_norm": 3.9462854862213135, "learning_rate": 8.946152120260265e-06, "loss": 0.8058, "step": 4750}, {"epoch": 1.064545047064097, "eval_loss": 0.8066560626029968, "eval_runtime": 318.2088, "eval_samples_per_second": 4.067, "eval_steps_per_second": 0.509, "eval_wer": 1.4506382978723404, "step": 4750}, {"epoch": 1.0701479157328553, "grad_norm": 5.651837348937988, "learning_rate": 8.94054296612071e-06, "loss": 0.8516, "step": 4775}, {"epoch": 1.0757507844016136, "grad_norm": 5.394379615783691, "learning_rate": 8.934933811981154e-06, "loss": 0.8465, "step": 4800}, {"epoch": 1.0757507844016136, "eval_loss": 0.8041602373123169, "eval_runtime": 337.3168, "eval_samples_per_second": 3.836, "eval_steps_per_second": 0.48, "eval_wer": 1.4900709219858157, "step": 4800}, {"epoch": 1.081353653070372, "grad_norm": 5.223489761352539, "learning_rate": 8.929324657841598e-06, "loss": 0.8792, "step": 4825}, {"epoch": 1.0869565217391304, "grad_norm": 6.445707321166992, "learning_rate": 8.923715503702044e-06, "loss": 0.8385, "step": 4850}, {"epoch": 1.0869565217391304, "eval_loss": 0.8024131059646606, "eval_runtime": 333.5758, "eval_samples_per_second": 3.879, "eval_steps_per_second": 0.486, "eval_wer": 1.5285106382978724, "step": 4850}, {"epoch": 1.0925593904078887, "grad_norm": 5.943089008331299, "learning_rate": 8.918106349562486e-06, "loss": 0.8798, "step": 4875}, {"epoch": 1.0981622590766473, "grad_norm": 8.560958862304688, "learning_rate": 8.912497195422932e-06, "loss": 0.8145, "step": 4900}, {"epoch": 1.0981622590766473, "eval_loss": 0.8011529445648193, "eval_runtime": 334.7469, "eval_samples_per_second": 3.866, "eval_steps_per_second": 0.484, "eval_wer": 1.4790070921985816, "step": 4900}, {"epoch": 1.1037651277454057, "grad_norm": 5.824674129486084, "learning_rate": 8.906888041283374e-06, "loss": 0.8571, "step": 4925}, {"epoch": 1.109367996414164, "grad_norm": 4.617836952209473, "learning_rate": 8.901278887143819e-06, "loss": 0.8548, "step": 4950}, {"epoch": 1.109367996414164, "eval_loss": 0.8027662038803101, "eval_runtime": 342.9417, "eval_samples_per_second": 3.773, "eval_steps_per_second": 0.472, "eval_wer": 1.555177304964539, "step": 4950}, {"epoch": 1.1149708650829224, "grad_norm": 6.073908805847168, "learning_rate": 8.895669733004264e-06, "loss": 0.8521, "step": 4975}, {"epoch": 1.1205737337516808, "grad_norm": 4.970393657684326, "learning_rate": 8.890060578864707e-06, "loss": 0.8578, "step": 5000}, {"epoch": 1.1205737337516808, "eval_loss": 0.8007997870445251, "eval_runtime": 323.2841, "eval_samples_per_second": 4.003, "eval_steps_per_second": 0.501, "eval_wer": 1.3141843971631206, "step": 5000}, {"epoch": 1.1261766024204394, "grad_norm": 4.646602153778076, "learning_rate": 8.884451424725153e-06, "loss": 0.85, "step": 5025}, {"epoch": 1.1317794710891977, "grad_norm": 6.34159517288208, "learning_rate": 8.878842270585595e-06, "loss": 0.8467, "step": 5050}, {"epoch": 1.1317794710891977, "eval_loss": 0.7995324730873108, "eval_runtime": 330.8804, "eval_samples_per_second": 3.911, "eval_steps_per_second": 0.49, "eval_wer": 1.4520567375886524, "step": 5050}, {"epoch": 1.137382339757956, "grad_norm": 6.1072306632995605, "learning_rate": 8.873233116446041e-06, "loss": 0.8625, "step": 5075}, {"epoch": 1.1429852084267145, "grad_norm": 4.108775615692139, "learning_rate": 8.867623962306485e-06, "loss": 0.8163, "step": 5100}, {"epoch": 1.1429852084267145, "eval_loss": 0.798009991645813, "eval_runtime": 330.7202, "eval_samples_per_second": 3.913, "eval_steps_per_second": 0.49, "eval_wer": 1.4782978723404254, "step": 5100}, {"epoch": 1.1485880770954728, "grad_norm": 4.659572124481201, "learning_rate": 8.86201480816693e-06, "loss": 0.7819, "step": 5125}, {"epoch": 1.1541909457642312, "grad_norm": 5.583585262298584, "learning_rate": 8.856405654027374e-06, "loss": 0.8928, "step": 5150}, {"epoch": 1.1541909457642312, "eval_loss": 0.7960417866706848, "eval_runtime": 325.78, "eval_samples_per_second": 3.972, "eval_steps_per_second": 0.497, "eval_wer": 1.4251063829787234, "step": 5150}, {"epoch": 1.1597938144329896, "grad_norm": 4.776031970977783, "learning_rate": 8.850796499887818e-06, "loss": 0.8333, "step": 5175}, {"epoch": 1.1653966831017482, "grad_norm": 6.666964054107666, "learning_rate": 8.845187345748262e-06, "loss": 0.8522, "step": 5200}, {"epoch": 1.1653966831017482, "eval_loss": 0.7958536148071289, "eval_runtime": 335.9008, "eval_samples_per_second": 3.852, "eval_steps_per_second": 0.482, "eval_wer": 1.4876595744680852, "step": 5200}, {"epoch": 1.1709995517705065, "grad_norm": 7.877660274505615, "learning_rate": 8.839578191608706e-06, "loss": 0.8246, "step": 5225}, {"epoch": 1.1766024204392649, "grad_norm": 5.683755874633789, "learning_rate": 8.83396903746915e-06, "loss": 0.8267, "step": 5250}, {"epoch": 1.1766024204392649, "eval_loss": 0.7945733666419983, "eval_runtime": 330.0526, "eval_samples_per_second": 3.921, "eval_steps_per_second": 0.491, "eval_wer": 1.4704964539007093, "step": 5250}, {"epoch": 1.1822052891080232, "grad_norm": 5.900104999542236, "learning_rate": 8.828359883329594e-06, "loss": 0.8392, "step": 5275}, {"epoch": 1.1878081577767818, "grad_norm": 5.60843563079834, "learning_rate": 8.822750729190039e-06, "loss": 0.8234, "step": 5300}, {"epoch": 1.1878081577767818, "eval_loss": 0.7928025722503662, "eval_runtime": 338.2062, "eval_samples_per_second": 3.826, "eval_steps_per_second": 0.479, "eval_wer": 1.5011347517730496, "step": 5300}, {"epoch": 1.1934110264455402, "grad_norm": 5.612999439239502, "learning_rate": 8.817141575050483e-06, "loss": 0.8258, "step": 5325}, {"epoch": 1.1990138951142986, "grad_norm": 4.684268474578857, "learning_rate": 8.811532420910927e-06, "loss": 0.7711, "step": 5350}, {"epoch": 1.1990138951142986, "eval_loss": 0.7939714789390564, "eval_runtime": 320.0908, "eval_samples_per_second": 4.043, "eval_steps_per_second": 0.506, "eval_wer": 1.4356028368794327, "step": 5350}, {"epoch": 1.204616763783057, "grad_norm": 3.9499523639678955, "learning_rate": 8.805923266771371e-06, "loss": 0.8207, "step": 5375}, {"epoch": 1.2102196324518153, "grad_norm": 5.336240291595459, "learning_rate": 8.800314112631815e-06, "loss": 0.8412, "step": 5400}, {"epoch": 1.2102196324518153, "eval_loss": 0.7922137975692749, "eval_runtime": 319.6082, "eval_samples_per_second": 4.049, "eval_steps_per_second": 0.507, "eval_wer": 1.384113475177305, "step": 5400}, {"epoch": 1.2158225011205737, "grad_norm": 4.8158278465271, "learning_rate": 8.794704958492261e-06, "loss": 0.8374, "step": 5425}, {"epoch": 1.221425369789332, "grad_norm": 6.009850978851318, "learning_rate": 8.789095804352704e-06, "loss": 0.8391, "step": 5450}, {"epoch": 1.221425369789332, "eval_loss": 0.7896971106529236, "eval_runtime": 318.965, "eval_samples_per_second": 4.057, "eval_steps_per_second": 0.508, "eval_wer": 1.4487943262411347, "step": 5450}, {"epoch": 1.2270282384580906, "grad_norm": 4.734396457672119, "learning_rate": 8.78348665021315e-06, "loss": 0.8491, "step": 5475}, {"epoch": 1.232631107126849, "grad_norm": 5.122131824493408, "learning_rate": 8.777877496073592e-06, "loss": 0.8528, "step": 5500}, {"epoch": 1.232631107126849, "eval_loss": 0.7879651188850403, "eval_runtime": 320.358, "eval_samples_per_second": 4.039, "eval_steps_per_second": 0.506, "eval_wer": 1.4571631205673758, "step": 5500}, {"epoch": 1.2382339757956073, "grad_norm": 6.056159496307373, "learning_rate": 8.772268341934038e-06, "loss": 0.8361, "step": 5525}, {"epoch": 1.2438368444643657, "grad_norm": 5.224989891052246, "learning_rate": 8.766659187794482e-06, "loss": 0.7698, "step": 5550}, {"epoch": 1.2438368444643657, "eval_loss": 0.7876822352409363, "eval_runtime": 325.0991, "eval_samples_per_second": 3.98, "eval_steps_per_second": 0.498, "eval_wer": 1.4890780141843971, "step": 5550}, {"epoch": 1.249439713133124, "grad_norm": 4.681524276733398, "learning_rate": 8.761050033654926e-06, "loss": 0.8021, "step": 5575}, {"epoch": 1.2550425818018827, "grad_norm": 5.204637050628662, "learning_rate": 8.75544087951537e-06, "loss": 0.8716, "step": 5600}, {"epoch": 1.2550425818018827, "eval_loss": 0.7868257164955139, "eval_runtime": 312.5189, "eval_samples_per_second": 4.141, "eval_steps_per_second": 0.518, "eval_wer": 1.4347517730496453, "step": 5600}, {"epoch": 1.260645450470641, "grad_norm": 4.52564001083374, "learning_rate": 8.749831725375814e-06, "loss": 0.7929, "step": 5625}, {"epoch": 1.2662483191393994, "grad_norm": 5.479198455810547, "learning_rate": 8.744222571236259e-06, "loss": 0.7966, "step": 5650}, {"epoch": 1.2662483191393994, "eval_loss": 0.7848247289657593, "eval_runtime": 312.9577, "eval_samples_per_second": 4.135, "eval_steps_per_second": 0.518, "eval_wer": 1.4080851063829787, "step": 5650}, {"epoch": 1.2718511878081578, "grad_norm": 3.9066059589385986, "learning_rate": 8.738613417096703e-06, "loss": 0.7974, "step": 5675}, {"epoch": 1.2774540564769161, "grad_norm": 4.246469020843506, "learning_rate": 8.733004262957147e-06, "loss": 0.8695, "step": 5700}, {"epoch": 1.2774540564769161, "eval_loss": 0.7868555188179016, "eval_runtime": 336.5037, "eval_samples_per_second": 3.845, "eval_steps_per_second": 0.481, "eval_wer": 1.517304964539007, "step": 5700}, {"epoch": 1.2830569251456745, "grad_norm": 6.353544235229492, "learning_rate": 8.727395108817591e-06, "loss": 0.8392, "step": 5725}, {"epoch": 1.2886597938144329, "grad_norm": 3.962284803390503, "learning_rate": 8.721785954678035e-06, "loss": 0.8315, "step": 5750}, {"epoch": 1.2886597938144329, "eval_loss": 0.7857937216758728, "eval_runtime": 343.474, "eval_samples_per_second": 3.767, "eval_steps_per_second": 0.472, "eval_wer": 1.5554609929078014, "step": 5750}, {"epoch": 1.2942626624831914, "grad_norm": 5.567563056945801, "learning_rate": 8.71617680053848e-06, "loss": 0.8217, "step": 5775}, {"epoch": 1.2998655311519498, "grad_norm": 5.969588279724121, "learning_rate": 8.710567646398924e-06, "loss": 0.8074, "step": 5800}, {"epoch": 1.2998655311519498, "eval_loss": 0.7871142029762268, "eval_runtime": 329.2357, "eval_samples_per_second": 3.93, "eval_steps_per_second": 0.492, "eval_wer": 1.4760283687943263, "step": 5800}, {"epoch": 1.3054683998207082, "grad_norm": 5.134238243103027, "learning_rate": 8.704958492259368e-06, "loss": 0.7993, "step": 5825}, {"epoch": 1.3110712684894665, "grad_norm": 4.21612548828125, "learning_rate": 8.699349338119812e-06, "loss": 0.8787, "step": 5850}, {"epoch": 1.3110712684894665, "eval_loss": 0.7826849818229675, "eval_runtime": 335.7013, "eval_samples_per_second": 3.855, "eval_steps_per_second": 0.483, "eval_wer": 1.5076595744680852, "step": 5850}, {"epoch": 1.3166741371582251, "grad_norm": 5.787265777587891, "learning_rate": 8.693740183980256e-06, "loss": 0.8136, "step": 5875}, {"epoch": 1.3222770058269835, "grad_norm": 5.7736077308654785, "learning_rate": 8.6881310298407e-06, "loss": 0.8682, "step": 5900}, {"epoch": 1.3222770058269835, "eval_loss": 0.7807874083518982, "eval_runtime": 336.6783, "eval_samples_per_second": 3.843, "eval_steps_per_second": 0.481, "eval_wer": 1.5157446808510637, "step": 5900}, {"epoch": 1.3278798744957419, "grad_norm": 4.176578998565674, "learning_rate": 8.682521875701146e-06, "loss": 0.81, "step": 5925}, {"epoch": 1.3334827431645002, "grad_norm": 5.430301189422607, "learning_rate": 8.677137087727172e-06, "loss": 0.8255, "step": 5950}, {"epoch": 1.3334827431645002, "eval_loss": 0.7808179259300232, "eval_runtime": 338.6658, "eval_samples_per_second": 3.821, "eval_steps_per_second": 0.478, "eval_wer": 1.5012765957446808, "step": 5950}, {"epoch": 1.3390856118332586, "grad_norm": 5.958614826202393, "learning_rate": 8.671527933587616e-06, "loss": 0.8476, "step": 5975}, {"epoch": 1.344688480502017, "grad_norm": 4.96903133392334, "learning_rate": 8.66591877944806e-06, "loss": 0.8698, "step": 6000}, {"epoch": 1.344688480502017, "eval_loss": 0.7806774973869324, "eval_runtime": 328.5104, "eval_samples_per_second": 3.939, "eval_steps_per_second": 0.493, "eval_wer": 1.5021276595744681, "step": 6000}, {"epoch": 1.3502913491707753, "grad_norm": 5.679050922393799, "learning_rate": 8.660309625308504e-06, "loss": 0.8067, "step": 6025}, {"epoch": 1.355894217839534, "grad_norm": 5.584303855895996, "learning_rate": 8.654700471168948e-06, "loss": 0.762, "step": 6050}, {"epoch": 1.355894217839534, "eval_loss": 0.7772865891456604, "eval_runtime": 327.8344, "eval_samples_per_second": 3.947, "eval_steps_per_second": 0.494, "eval_wer": 1.3970212765957446, "step": 6050}, {"epoch": 1.3614970865082923, "grad_norm": 6.412964820861816, "learning_rate": 8.649091317029393e-06, "loss": 0.8125, "step": 6075}, {"epoch": 1.3670999551770506, "grad_norm": 4.921046257019043, "learning_rate": 8.643482162889837e-06, "loss": 0.7985, "step": 6100}, {"epoch": 1.3670999551770506, "eval_loss": 0.7774174809455872, "eval_runtime": 325.1209, "eval_samples_per_second": 3.98, "eval_steps_per_second": 0.498, "eval_wer": 1.4170212765957446, "step": 6100}], "logging_steps": 25, "max_steps": 44620, "num_input_tokens_seen": 0, "num_train_epochs": 10, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.242849705984e+18, "train_batch_size": 4, "trial_name": null, "trial_params": null}