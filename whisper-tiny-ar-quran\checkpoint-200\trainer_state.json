{"best_global_step": 200, "best_metric": 1.0804255319148937, "best_model_checkpoint": "./whisper-tiny-ar-quran/checkpoint-200", "epoch": 0.04482294935006723, "eval_steps": 50, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.005602868668758404, "grad_norm": 21.66828155517578, "learning_rate": 4.4e-06, "loss": 4.3294, "step": 25}, {"epoch": 0.011205737337516808, "grad_norm": 15.695514678955078, "learning_rate": 9.4e-06, "loss": 3.7954, "step": 50}, {"epoch": 0.011205737337516808, "eval_loss": 3.415271043777466, "eval_runtime": 221.408, "eval_samples_per_second": 5.844, "eval_steps_per_second": 0.732, "eval_wer": 1.184822695035461, "step": 50}, {"epoch": 0.016808606006275213, "grad_norm": 8.252846717834473, "learning_rate": 9.995063944357191e-06, "loss": 3.2702, "step": 75}, {"epoch": 0.022411474675033616, "grad_norm": 9.971335411071777, "learning_rate": 9.989454790217637e-06, "loss": 2.8812, "step": 100}, {"epoch": 0.022411474675033616, "eval_loss": 2.6263279914855957, "eval_runtime": 212.0523, "eval_samples_per_second": 6.102, "eval_steps_per_second": 0.764, "eval_wer": 1.1092198581560284, "step": 100}, {"epoch": 0.028014343343792023, "grad_norm": 6.732166767120361, "learning_rate": 9.98384563607808e-06, "loss": 2.4738, "step": 125}, {"epoch": 0.033617212012550426, "grad_norm": 5.674781799316406, "learning_rate": 9.978236481938525e-06, "loss": 2.153, "step": 150}, {"epoch": 0.033617212012550426, "eval_loss": 2.0279593467712402, "eval_runtime": 211.6211, "eval_samples_per_second": 6.115, "eval_steps_per_second": 0.766, "eval_wer": 1.1053900709219857, "step": 150}, {"epoch": 0.03922008068130883, "grad_norm": 4.94719934463501, "learning_rate": 9.972627327798968e-06, "loss": 2.0401, "step": 175}, {"epoch": 0.04482294935006723, "grad_norm": 4.771636962890625, "learning_rate": 9.967018173659414e-06, "loss": 1.8955, "step": 200}, {"epoch": 0.04482294935006723, "eval_loss": 1.7179163694381714, "eval_runtime": 208.6924, "eval_samples_per_second": 6.201, "eval_steps_per_second": 0.776, "eval_wer": 1.0804255319148937, "step": 200}], "logging_steps": 25, "max_steps": 44620, "num_input_tokens_seen": 0, "num_train_epochs": 10, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 4.0749170688e+16, "train_batch_size": 4, "trial_name": null, "trial_params": null}